package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlin.math.abs

/**
 * 滚轮选择器的状态管理类
 * 
 * @param initialIndex 初始选中的索引
 * @param itemCount 数据项总数
 * @param isInfinite 是否启用无限循环模式
 */
@Stable
class WheelPickerState(
    initialIndex: Int = 0,
    private val itemCount: Int,
    private val isInfinite: Boolean = false
) {
    
    // 内部LazyList状态
    internal val lazyListState: LazyListState
    
    // 当前数据项总数
    internal var currentItemCount by mutableStateOf(itemCount)
        private set
    
    // 是否为无限循环模式
    internal var currentIsInfinite by mutableStateOf(isInfinite)
        private set
    
    // 无限循环时的虚拟列表大小倍数
    private val infiniteMultiplier = 10000
    
    // 无限循环时的中心偏移量
    private val infiniteCenterOffset: Int
        get() = if (currentIsInfinite && currentItemCount > 0) {
            (infiniteMultiplier / 2) * currentItemCount
        } else 0
    
    // 无限循环时的虚拟列表总大小
    internal val virtualItemCount: Int
        get() = if (currentIsInfinite && currentItemCount > 0) {
            currentItemCount * infiniteMultiplier
        } else currentItemCount
    
    init {
        val startIndex = if (isInfinite && itemCount > 0) {
            infiniteCenterOffset + (initialIndex.coerceIn(0, itemCount - 1))
        } else {
            initialIndex.coerceIn(0, maxOf(0, itemCount - 1))
        }
        lazyListState = LazyListState(startIndex)
    }
    
    /**
     * 当前是否正在滚动
     */
    val isScrollInProgress: Boolean by derivedStateOf {
        lazyListState.isScrollInProgress
    }

    /**
     * 当前实时的索引（滚动过程中会实时变化）
     */
    val currentIndex: Int by derivedStateOf {
        val currentIndex = getCurrentVisibleIndex()
        if (currentIsInfinite && currentItemCount > 0) {
            // 将无限循环的虚拟索引映射回实际数据索引
            ((currentIndex % currentItemCount) + currentItemCount) % currentItemCount
        } else {
            currentIndex.coerceIn(0, maxOf(0, currentItemCount - 1))
        }
    }

    /**
     * 最终选中的数据索引（只在滚动完全停止后更新）
     * 这是外部获取选择结果的主要属性
     */
    var snappedIndex by mutableStateOf(initialIndex.coerceIn(0, maxOf(0, itemCount - 1)))
        private set
    
    /**
     * 获取当前可见的中心项索引
     * 使用remember来避免layoutInfo的频繁重组问题
     */
    private fun getCurrentVisibleIndex(): Int {
        val layoutInfo = lazyListState.layoutInfo
        val viewportCenter = layoutInfo.viewportStartOffset + layoutInfo.viewportSize.height / 2

        // 找到最接近视口中心的item
        val centerItem = layoutInfo.visibleItemsInfo.minByOrNull { itemInfo ->
            val itemCenter = itemInfo.offset + itemInfo.size / 2
            abs(itemCenter - viewportCenter)
        }

        return centerItem?.index ?: 0
    }

    init {
        // 监听滚动状态变化，在停止滚动时更新snappedIndex
        MainScope().launch {
            snapshotFlow { isScrollInProgress }
                .collect { scrolling ->
                    if (!scrolling) {
                        // 滚动停止，更新最终选中索引
                        snappedIndex = currentIndex
                    }
                }
        }
    }
    
    /**
     * 立即滚动到指定索引（无动画）
     */
    suspend fun scrollToItem(index: Int) {
        val targetIndex = calculateTargetIndex(index)
        lazyListState.scrollToItem(targetIndex)
    }
    
    /**
     * 平滑滚动到指定索引（有动画）
     */
    suspend fun animateScrollToItem(index: Int) {
        val targetIndex = calculateTargetIndex(index)
        lazyListState.animateScrollToItem(targetIndex)
    }
    
    /**
     * 计算目标索引（处理无限循环）
     */
    private fun calculateTargetIndex(index: Int): Int {
        val safeIndex = index.coerceIn(0, maxOf(0, currentItemCount - 1))
        return if (currentIsInfinite && currentItemCount > 0) {
            val currentVirtualIndex = getCurrentVisibleIndex()
            val currentRealIndex = ((currentVirtualIndex % currentItemCount) + currentItemCount) % currentItemCount
            val diff = safeIndex - currentRealIndex
            
            // 选择最近的路径
            val targetVirtualIndex = when {
                abs(diff) <= currentItemCount / 2 -> currentVirtualIndex + diff
                diff > 0 -> currentVirtualIndex + diff - currentItemCount
                else -> currentVirtualIndex + diff + currentItemCount
            }
            
            targetVirtualIndex.coerceIn(0, virtualItemCount - 1)
        } else {
            safeIndex
        }
    }
    
    /**
     * 更新数据源（用于多级联动）
     */
    suspend fun updateData(newItemCount: Int, newIsInfinite: Boolean = currentIsInfinite) {
        if (newItemCount == currentItemCount && newIsInfinite == currentIsInfinite) return
        
        val oldSnappedIndex = snappedIndex
        currentItemCount = newItemCount
        currentIsInfinite = newIsInfinite
        
        // 调整当前选中项，避免索引越界
        val adjustedIndex = if (newItemCount > 0) {
            oldSnappedIndex.coerceIn(0, newItemCount - 1)
        } else 0
        
        // 重新定位到调整后的索引
        if (newItemCount > 0) {
            val targetIndex = if (newIsInfinite) {
                infiniteCenterOffset + adjustedIndex
            } else {
                adjustedIndex
            }
            
            // 使用协程作用域来执行滚动
            lazyListState.scrollToItem(targetIndex)
        }
    }
    
    /**
     * 将虚拟索引转换为实际数据索引
     */
    internal fun virtualToRealIndex(virtualIndex: Int): Int {
        return if (currentIsInfinite && currentItemCount > 0) {
            ((virtualIndex % currentItemCount) + currentItemCount) % currentItemCount
        } else {
            virtualIndex.coerceIn(0, maxOf(0, currentItemCount - 1))
        }
    }
    
    companion object {
        /**
         * Saver for state restoration
         */
        fun Saver(itemCount: Int, isInfinite: Boolean): Saver<WheelPickerState, *> = Saver(
            save = { it.snappedIndex },
            restore = { WheelPickerState(it, itemCount, isInfinite) }
        )
    }
}

/**
 * 创建并记住WheelPickerState
 */
@Composable
fun rememberWheelPickerState(
    initialIndex: Int = 0,
    itemCount: Int,
    isInfinite: Boolean = false
): WheelPickerState {
    return rememberSaveable(
        saver = WheelPickerState.Saver(itemCount, isInfinite)
    ) {
        WheelPickerState(initialIndex, itemCount, isInfinite)
    }
}

package com.buque.wakoo.ui.screens.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.R
import com.buque.wakoo.ext.showToast
import com.buque.wakoo.ui.icons.UserFemale
import com.buque.wakoo.ui.icons.UserMale
import com.buque.wakoo.ui.icons.WakooIcons
import com.buque.wakoo.ui.theme.MI_SANS
import com.buque.wakoo.ui.theme.WakooBlack
import com.buque.wakoo.ui.theme.WakooGrayText
import com.buque.wakoo.ui.theme.WakooLightGrayBg
import com.buque.wakoo.ui.theme.WakooTheme
import com.buque.wakoo.ui.theme.WakooWhite
import com.buque.wakoo.ui.widget.AppTextField
import com.buque.wakoo.ui.widget.GradientButton
import com.buque.wakoo.ui.widget.ImeButtonScaffold
import com.buque.wakoo.ui.widget.SizeHeight
import com.buque.wakoo.ui.widget.SizeWidth
import com.buque.wakoo.ui.widget.TitleScreenScaffold
import com.buque.wakoo.ui.widget.WakooTitleBarDefaults

enum class Gender {
    MALE,
    FEMALE,
    NOT_SELECTED,
    ;

    fun toIntVale() = ordinal + 1
}

@Composable
fun RegisterFillInfoScreen(onComplete: (nickname: String?, gender: Int?) -> Unit = { _, _ -> }) {
    var nickname by remember { mutableStateOf("") }
    var gender by remember { mutableStateOf(Gender.NOT_SELECTED) }

    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    LaunchedEffect(Unit) {
        focusRequester.requestFocus() // 请求焦点
        keyboardController?.show() // 请求显示键盘
    }

    fun callComplete() {
        if (nickname.isNotEmpty() && gender != Gender.NOT_SELECTED) {
            focusManager.clearFocus()
            onComplete(
                nickname,
                gender.toIntVale(),
            )
        } else {
            showToast("请输入完整资料")
        }
    }

    Box(
        modifier =
            Modifier
                .fillMaxSize()
                .background(WakooWhite)
                .paint(
                    painter = painterResource(id = R.drawable.bg_common_top),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter,
                ),
    ) {
        TitleScreenScaffold(
            title = "",
            actions = {
                WakooTitleBarDefaults.TextButtonAction("跳过", onClick = {
                    focusManager.clearFocus()
                    onComplete(null, null)
                })
            },
            containerColor = Color.Transparent,
        ) { paddingValues ->

            ImeButtonScaffold(
                buttonContent = {
                    // 底部按钮
                    GradientButton(
                        text = "进入wakoo",
                        height = 56.dp,
                        enabled = nickname.isNotEmpty() && gender != Gender.NOT_SELECTED,
                        onClick = ::callComplete,
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .align(Alignment.BottomCenter)
                                .padding(horizontal = 28.dp),
                    )
                },
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                buttonModifier =
                    Modifier
                        .fillMaxWidth()
                        .background(Color(0xFFFFFFFF))
                        .padding(
                            top = 20.dp,
                            bottom = 15.dp,
                        ),
                buttonContentMinimumPadding = 120.dp,
            ) {
                // 主要内容
                Column(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 28.dp)
                            .padding(top = 16.dp),
                ) {
                    // 标题
                    Text(
                        text = "请完善您的资料",
                        color = WakooBlack,
                        fontWeight = FontWeight.Bold,
                        fontSize = 24.sp,
                        style = MaterialTheme.typography.titleLarge,
                        fontFamily = FontFamily.MI_SANS,
                    )

                    SizeHeight(12.dp)

                    Text(
                        text = "为您更精确的推荐内容",
                        color = WakooGrayText,
                        fontSize = 14.sp,
                    )

                    SizeHeight(60.dp)

                    // 昵称输入
                    Text(
                        text = "昵称",
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    AppTextField(
                        value = nickname,
                        onValueChange = {
                            nickname = it
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(48.dp)
                                .focusRequester(focusRequester),
                        maxLength = 9,
                        showLengthTip = false,
                        shape = CircleShape,
                        placeholder = "请输入您的网络昵称",
                        backgroundColor = WakooLightGrayBg,
                        textStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFF111111),
                                lineHeight = 21.sp,
                            ),
                        placeholderStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFFB6B6B6),
                                lineHeight = 21.sp,
                                fontWeight = FontWeight.Normal,
                            ),
                        keyboardOptions =
                            KeyboardOptions(
                                imeAction = ImeAction.Done,
                            ),
                        singleLine = true,
                        keyboardActions =
                            KeyboardActions(
                                onDone = {
                                    callComplete()
                                },
                            ),
                    )

                    SizeHeight(16.dp)

                    // 性别选择
                    Text(
                        text = "性别",
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                    ) {
                        // 男性选项
                        GenderOption(
                            icon = {
                                Image(
                                    imageVector = WakooIcons.UserMale,
                                    contentDescription = null,
                                    modifier = Modifier.size(24.dp),
                                )
                            },
                            text = "男",
                            isSelected = gender == Gender.MALE,
                            onClick = { gender = Gender.MALE },
                        )

                        SizeWidth(16.dp)

                        // 女性选项
                        GenderOption(
                            icon = {
                                Image(
                                    imageVector = WakooIcons.UserFemale,
                                    contentDescription = null,
                                    modifier = Modifier.size(24.dp),
                                )
                            },
                            text = "女",
                            isSelected = gender == Gender.FEMALE,
                            onClick = { gender = Gender.FEMALE },
                        )
                    }
                    SizeHeight(16.dp)

                    // 昵称输入
                    Text(
                        text = "出生日期",
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    AppTextField(
                        value = nickname,
                        onValueChange = {
                            nickname = it
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(48.dp)
                                .focusRequester(focusRequester),
                        maxLength = 9,
                        showLengthTip = false,
                        shape = CircleShape,
                        backgroundColor = WakooLightGrayBg,
                        keyboardOptions =
                            KeyboardOptions(
                                imeAction = ImeAction.Done,
                            ),
                        singleLine = true,
                        keyboardActions =
                            KeyboardActions(
                                onDone = {
                                    callComplete()
                                },
                            ),
                    )

                    SizeHeight(16.dp)

                    // 昵称输入
                    Text(
                        text = "邀请码（选填）",
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                    )

                    SizeHeight(8.dp)

                    AppTextField(
                        value = nickname,
                        onValueChange = {
                            nickname = it
                        },
                        modifier =
                            Modifier
                                .fillMaxWidth()
                                .height(48.dp)
                                .focusRequester(focusRequester),
                        maxLength = 9,
                        placeholder = "请输入邀请码",
                        showLengthTip = false,
                        shape = CircleShape,
                        backgroundColor = WakooLightGrayBg,
                        textStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFF111111),
                                lineHeight = 21.sp,
                            ),
                        placeholderStyle =
                            MaterialTheme.typography.titleSmall.merge(
                                color = Color(0xFFB6B6B6),
                                lineHeight = 21.sp,
                                fontWeight = FontWeight.Normal,
                            ),
                        keyboardOptions =
                            KeyboardOptions(
                                imeAction = ImeAction.Done,
                            ),
                        singleLine = true,
                        keyboardActions =
                            KeyboardActions(
                                onDone = {
                                    callComplete()
                                },
                            ),
                    )

                    SizeHeight(16.dp)
                }
            }
        }
    }
}

@Composable
fun GenderOption(
    icon: @Composable () -> Unit,
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier =
            modifier
                .clip(RoundedCornerShape(24.dp))
                .background(if (isSelected) WakooBlack else WakooLightGrayBg)
                .clickable(onClick = onClick)
                .padding(
                    horizontal = 16.dp,
                    vertical = 8.dp,
                ),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier.size(24.dp),
            contentAlignment = Alignment.Center,
        ) {
            icon()
        }

        SizeWidth(8.dp)

        Text(
            text = text,
            color = if (isSelected) WakooWhite else WakooBlack,
            fontSize = 16.sp,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ProfileCompletionScreenPreview() {
    WakooTheme {
        RegisterFillInfoScreen()
    }
}

package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * WheelPicker的默认配置
 */
object WheelPickerDefaults {
    @Suppress("ktlint:standard:property-naming")
    /**
     * 默认可见项数量（必须为奇数）
     */
    const val DefaultVisibleItemsCount = 5

    /**
     * 默认项高度
     */
    val DefaultItemHeight = 48.dp

    /**
     * 默认分割线颜色
     */
    val DefaultDividerColor = Color(0xFFE5E5E5)

    /**
     * 默认分割线厚度
     */
    val DefaultDividerThickness = 1.dp

    /**
     * 默认文本样式
     */
    @Composable
    fun defaultTextStyle(): TextStyle = MaterialTheme.typography.bodyLarge

    /**
     * 默认选中文本颜色
     */
    @Composable
    fun defaultSelectedTextColor(): Color = LocalContentColor.current

    /**
     * 默认未选中文本颜色
     */
    @Composable
    fun defaultUnselectedTextColor(): Color = LocalContentColor.current.copy(alpha = 0.6f)

    /**
     * 3D效果配置
     */
    @Immutable
    data class Effect3DConfig(
        val enabled: Boolean = true,
        val maxScale: Float = 1.0f,
        val minScale: Float = 0.8f,
        val maxAlpha: Float = 1.0f,
        val minAlpha: Float = 0.3f,
        val maxRotationX: Float = 0f,
        val minRotationX: Float = -15f,
    ) {
        init {
            require(maxScale >= minScale) { "maxScale must be >= minScale" }
            require(maxAlpha >= minAlpha) { "maxAlpha must be >= minAlpha" }
            require(maxRotationX >= minRotationX) { "maxRotationX must be >= minRotationX" }
        }
    }

    /**
     * 默认3D效果配置
     */
    fun default3DEffectConfig(): Effect3DConfig = Effect3DConfig()

    /**
     * 分割线配置
     */
    @Immutable
    data class DividerConfig(
        val enabled: Boolean = true,
        val color: Color = DefaultDividerColor,
        val thickness: Dp = DefaultDividerThickness,
    )

    /**
     * 默认分割线配置
     */
    @Composable
    fun defaultDividerConfig(): DividerConfig = DividerConfig()
}

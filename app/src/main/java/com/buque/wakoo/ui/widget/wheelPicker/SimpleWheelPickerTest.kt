package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.buque.wakoo.ui.theme.WakooTheme

/**
 * 简单的WheelPicker测试组件
 */
@Composable
fun SimpleWheelPickerTest() {
    var selectedItem by remember { mutableStateOf("") }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "WheelPicker 测试",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        val items = remember {
            listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
        }
        
        val state = rememberWheelPickerState(
            initialIndex = 0,
            itemCount = items.size
        )
        
        // 监听选择变化
        LaunchedEffect(state.snappedIndex) {
            if (!state.isScrollInProgress) {
                selectedItem = items.getOrNull(state.snappedIndex) ?: ""
            }
        }
        
        WheelPicker(
            items = items,
            state = state,
            modifier = Modifier.fillMaxWidth(),
            visibleItemsCount = 5,
            itemHeight = 48.dp
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        Text(
            text = "选中项: $selectedItem",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "当前索引: ${state.snappedIndex}",
            style = MaterialTheme.typography.bodyMedium
        )
        
        Text(
            text = "是否滚动中: ${state.isScrollInProgress}",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun SimpleWheelPickerTestPreview() {
    WakooTheme {
        SimpleWheelPickerTest()
    }
}

package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import java.time.LocalDate
import java.time.YearMonth

/**
 * 日期滚轮选择器组件
 * 支持年、月、日三级联动选择
 * 
 * @param selectedDate 当前选中的日期
 * @param onDateChanged 日期改变回调
 * @param modifier 修饰符
 * @param yearRange 年份范围
 * @param itemHeight 每个项的高度
 * @param visibleItemsCount 可见项数量
 * @param itemTextStyle 项文本样式
 * @param dividerConfig 分割线配置
 * @param effect3DConfig 3D效果配置
 */
@Composable
fun DateWheelPicker(
    modifier: Modifier = Modifier,
    selectedDate: LocalDate = LocalDate.now(),
    onDateChanged: (LocalDate) -> Unit = {},
    yearRange: IntRange = (1900..2100),
    itemHeight: Dp = WheelPickerDefaults.DefaultItemHeight,
    visibleItemsCount: Int = WheelPickerDefaults.DefaultVisibleItemsCount,
    itemTextStyle: TextStyle = WheelPickerDefaults.defaultTextStyle(),
    dividerConfig: WheelPickerDefaults.DividerConfig = WheelPickerDefaults.defaultDividerConfig(),
    effect3DConfig: WheelPickerDefaults.Effect3DConfig = WheelPickerDefaults.default3DEffectConfig()
) {
    // 年份数据
    val years = remember(yearRange) { yearRange.toList() }
    
    // 月份数据
    val months = remember { (1..12).toList() }
    
    // 当前选中的年月，用于计算天数
    var currentYear by remember(selectedDate) { mutableStateOf(selectedDate.year) }
    var currentMonth by remember(selectedDate) { mutableStateOf(selectedDate.monthValue) }
    
    // 根据年月计算天数
    val days = remember(currentYear, currentMonth) {
        val yearMonth = YearMonth.of(currentYear, currentMonth)
        (1..yearMonth.lengthOfMonth()).toList()
    }
    
    // 创建状态
    val yearState = rememberWheelPickerState(
        initialIndex = years.indexOf(selectedDate.year).coerceAtLeast(0),
        itemCount = years.size
    )
    
    val monthState = rememberWheelPickerState(
        initialIndex = selectedDate.monthValue - 1,
        itemCount = months.size
    )
    
    val dayState = rememberWheelPickerState(
        initialIndex = selectedDate.dayOfMonth - 1,
        itemCount = days.size
    )
    
    // 监听年份变化 - 只在滚动停止后触发
    LaunchedEffect(yearState.snappedIndex) {
        val newYear = years.getOrNull(yearState.snappedIndex) ?: currentYear
        if (newYear != currentYear) {
            currentYear = newYear
            // 触发日期变化回调
            val newDate = createSafeDate(currentYear, currentMonth, dayState.snappedIndex + 1)
            onDateChanged(newDate)
        }
    }

    // 监听月份变化 - 只在滚动停止后触发
    LaunchedEffect(monthState.snappedIndex) {
        val newMonth = months.getOrNull(monthState.snappedIndex) ?: currentMonth
        if (newMonth != currentMonth) {
            currentMonth = newMonth
            // 触发日期变化回调
            val newDate = createSafeDate(currentYear, currentMonth, dayState.snappedIndex + 1)
            onDateChanged(newDate)
        }
    }

    // 监听日期变化 - 只在滚动停止后触发
    LaunchedEffect(dayState.snappedIndex) {
        val newDay = dayState.snappedIndex + 1
        val newDate = createSafeDate(currentYear, currentMonth, newDay)
        onDateChanged(newDate)
    }
    
    // 当天数变化时，调整日期选择器
    LaunchedEffect(days.size) {
        dayState.updateData(days.size, false)
        // 如果当前选中的日期超出了新月份的天数范围，调整到最后一天
        if (dayState.snappedIndex >= days.size) {
            dayState.animateScrollToItem(days.size - 1)
        }
    }
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 年份选择器
        WheelPicker(
            items = years,
            state = yearState,
            modifier = Modifier.weight(1f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            itemContent = { year, isSelected ->
                Text(
                    text = "${year}年",
                    style = itemTextStyle,
                    color = if (isSelected) {
                        WheelPickerDefaults.defaultSelectedTextColor()
                    } else {
                        WheelPickerDefaults.defaultUnselectedTextColor()
                    },
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )
            }
        )
        
        // 月份选择器
        WheelPicker(
            items = months,
            state = monthState,
            modifier = Modifier.weight(1f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            itemContent = { month, isSelected ->
                Text(
                    text = "${month}月",
                    style = itemTextStyle,
                    color = if (isSelected) {
                        WheelPickerDefaults.defaultSelectedTextColor()
                    } else {
                        WheelPickerDefaults.defaultUnselectedTextColor()
                    },
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )
            }
        )
        
        // 日期选择器
        WheelPicker(
            items = days,
            state = dayState,
            modifier = Modifier.weight(1f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            itemContent = { day, isSelected ->
                Text(
                    text = "${day}日",
                    style = itemTextStyle,
                    color = if (isSelected) {
                        WheelPickerDefaults.defaultSelectedTextColor()
                    } else {
                        WheelPickerDefaults.defaultUnselectedTextColor()
                    },
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )
            }
        )
    }
}

/**
 * 创建安全的日期，避免无效日期（如2月30日）
 */
private fun createSafeDate(year: Int, month: Int, day: Int): LocalDate {
    val yearMonth = YearMonth.of(year, month)
    val safeDay = day.coerceIn(1, yearMonth.lengthOfMonth())
    return LocalDate.of(year, month, safeDay)
}

/**
 * 时间滚轮选择器组件
 * 支持时、分、秒三级选择
 * 
 * @param selectedHour 当前选中的小时
 * @param selectedMinute 当前选中的分钟
 * @param selectedSecond 当前选中的秒
 * @param onTimeChanged 时间改变回调
 * @param modifier 修饰符
 * @param is24Hour 是否使用24小时制
 * @param showSeconds 是否显示秒
 * @param itemHeight 每个项的高度
 * @param visibleItemsCount 可见项数量
 * @param itemTextStyle 项文本样式
 * @param dividerConfig 分割线配置
 * @param effect3DConfig 3D效果配置
 */
@Composable
fun TimeWheelPicker(
    modifier: Modifier = Modifier,
    selectedHour: Int = 0,
    selectedMinute: Int = 0,
    selectedSecond: Int = 0,
    onTimeChanged: (hour: Int, minute: Int, second: Int) -> Unit = { _, _, _ -> },
    is24Hour: Boolean = true,
    showSeconds: Boolean = true,
    itemHeight: Dp = WheelPickerDefaults.DefaultItemHeight,
    visibleItemsCount: Int = WheelPickerDefaults.DefaultVisibleItemsCount,
    itemTextStyle: TextStyle = WheelPickerDefaults.defaultTextStyle(),
    dividerConfig: WheelPickerDefaults.DividerConfig = WheelPickerDefaults.defaultDividerConfig(),
    effect3DConfig: WheelPickerDefaults.Effect3DConfig = WheelPickerDefaults.default3DEffectConfig()
) {
    // 小时数据
    val hours = remember(is24Hour) {
        if (is24Hour) (0..23).toList() else (1..12).toList()
    }
    
    // 分钟和秒数据
    val minutes = remember { (0..59).toList() }
    val seconds = remember { (0..59).toList() }
    
    // 创建状态
    val hourState = rememberWheelPickerState(
        initialIndex = if (is24Hour) selectedHour else (selectedHour - 1).coerceIn(0, 11),
        itemCount = hours.size,
        isInfinite = true
    )
    
    val minuteState = rememberWheelPickerState(
        initialIndex = selectedMinute,
        itemCount = minutes.size,
        isInfinite = true
    )
    
    val secondState = rememberWheelPickerState(
        initialIndex = selectedSecond,
        itemCount = seconds.size,
        isInfinite = true
    )
    
    // 监听时间变化
    LaunchedEffect(hourState.snappedIndex, minuteState.snappedIndex, secondState.snappedIndex) {
        if (!hourState.isScrollInProgress && !minuteState.isScrollInProgress && !secondState.isScrollInProgress) {
            val hour = hours.getOrNull(hourState.snappedIndex) ?: 0
            val minute = minutes.getOrNull(minuteState.snappedIndex) ?: 0
            val second = seconds.getOrNull(secondState.snappedIndex) ?: 0
            onTimeChanged(hour, minute, second)
        }
    }
    
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 小时选择器
        WheelPicker(
            items = hours,
            state = hourState,
            modifier = Modifier.weight(1f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            isInfinite = true,
            itemContent = { hour, isSelected ->
                Text(
                    text = String.format("%02d时", hour),
                    style = itemTextStyle,
                    color = if (isSelected) {
                        WheelPickerDefaults.defaultSelectedTextColor()
                    } else {
                        WheelPickerDefaults.defaultUnselectedTextColor()
                    },
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )
            }
        )
        
        // 分钟选择器
        WheelPicker(
            items = minutes,
            state = minuteState,
            modifier = Modifier.weight(1f),
            visibleItemsCount = visibleItemsCount,
            itemHeight = itemHeight,
            itemTextStyle = itemTextStyle,
            dividerConfig = dividerConfig,
            effect3DConfig = effect3DConfig,
            isInfinite = true,
            itemContent = { minute, isSelected ->
                Text(
                    text = String.format("%02d分", minute),
                    style = itemTextStyle,
                    color = if (isSelected) {
                        WheelPickerDefaults.defaultSelectedTextColor()
                    } else {
                        WheelPickerDefaults.defaultUnselectedTextColor()
                    },
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )
            }
        )
        
        // 秒选择器（可选）
        if (showSeconds) {
            WheelPicker(
                items = seconds,
                state = secondState,
                modifier = Modifier.weight(1f),
                visibleItemsCount = visibleItemsCount,
                itemHeight = itemHeight,
                itemTextStyle = itemTextStyle,
                dividerConfig = dividerConfig,
                effect3DConfig = effect3DConfig,
                isInfinite = true,
                itemContent = { second, isSelected ->
                    Text(
                        text = String.format("%02d秒", second),
                        style = itemTextStyle,
                        color = if (isSelected) {
                            WheelPickerDefaults.defaultSelectedTextColor()
                        } else {
                            WheelPickerDefaults.defaultUnselectedTextColor()
                        },
                        textAlign = TextAlign.Center,
                        maxLines = 1
                    )
                }
            )
        }
    }
}

package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.animation.core.AnimationState
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDecay
import androidx.compose.animation.core.animateTo
import androidx.compose.animation.core.spring
import androidx.compose.animation.splineBasedDecay
import androidx.compose.foundation.gestures.FlingBehavior
import androidx.compose.foundation.gestures.ScrollScope
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlin.math.abs
import kotlin.math.sign

/**
 * 通用滚轮选择器组件
 *
 * @param items 数据源列表
 * @param state 滚轮选择器状态
 * @param modifier 修饰符
 * @param visibleItemsCount 可见项数量（必须为奇数）
 * @param itemHeight 每个项的高度
 * @param itemTextStyle 项文本样式
 * @param selectedTextColor 选中项文本颜色
 * @param unselectedTextColor 未选中项文本颜色
 * @param dividerConfig 分割线配置
 * @param effect3DConfig 3D效果配置
 * @param isInfinite 是否启用无限循环
 * @param isItemEnabled 判断指定索引的项是否可用的函数
 * @param itemContent 自定义项内容
 */
@Composable
fun <T> WheelPicker(
    items: List<T>,
    state: WheelPickerState,
    modifier: Modifier = Modifier,
    visibleItemsCount: Int = WheelPickerDefaults.DefaultVisibleItemsCount,
    itemHeight: Dp = WheelPickerDefaults.DefaultItemHeight,
    itemTextStyle: TextStyle = WheelPickerDefaults.defaultTextStyle(),
    selectedTextColor: Color = WheelPickerDefaults.defaultSelectedTextColor(),
    unselectedTextColor: Color = WheelPickerDefaults.defaultUnselectedTextColor(),
    dividerConfig: WheelPickerDefaults.DividerConfig = WheelPickerDefaults.defaultDividerConfig(),
    effect3DConfig: WheelPickerDefaults.Effect3DConfig = WheelPickerDefaults.default3DEffectConfig(),
    isInfinite: Boolean = false,
    isItemEnabled: (Int) -> Boolean = { true },
    itemContent: @Composable (item: T, isSelected: Boolean, isEnabled: Boolean) -> Unit = { item, isSelected, isEnabled ->
        Text(
            text = item.toString(),
            style = itemTextStyle,
            color = when {
                !isEnabled -> unselectedTextColor.copy(alpha = 0.3f)
                isSelected -> selectedTextColor
                else -> unselectedTextColor
            },
            textAlign = TextAlign.Center,
            maxLines = 1,
        )
    },
) {
    require(visibleItemsCount % 2 == 1) { "visibleItemsCount must be odd" }
    require(items.isNotEmpty()) { "items cannot be empty" }

    // 更新状态中的数据
    LaunchedEffect(items.size, isInfinite) {
        state.updateData(items.size, isInfinite)
    }

    // 监听滚动状态变化，更新snappedIndex
    LaunchedEffect(state.isScrollInProgress) {
        state.updateSnappedIndexIfStopped()
    }

    val totalHeight = itemHeight * visibleItemsCount
    val halfVisibleItems = visibleItemsCount / 2

    Box(
        modifier = modifier.height(totalHeight),
        contentAlignment = Alignment.Center,
    ) {
        // 主要的滚轮列表
        LazyColumn(
            state = state.lazyListState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = itemHeight * halfVisibleItems),
            flingBehavior =
                wheelPickerFlingBehavior(
                    state = state,
                    itemHeight = itemHeight,
                ),
        ) {
            items(state.virtualItemCount) { virtualIndex ->
                val realIndex = state.virtualToRealIndex(virtualIndex)
                val item = items.getOrNull(realIndex)

                if (item != null) {
                    WheelPickerItem(
                        item = item,
                        virtualIndex = virtualIndex,
                        realIndex = realIndex,
                        state = state,
                        itemHeight = itemHeight,
                        visibleItemsCount = visibleItemsCount,
                        effect3DConfig = effect3DConfig,
                        isItemEnabled = isItemEnabled,
                        itemContent = itemContent,
                    )
                }
            }
        }

        // 分割线
        if (dividerConfig.enabled) {
            Column {
                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    thickness = dividerConfig.thickness,
                    color = dividerConfig.color,
                )

                Spacer(modifier = Modifier.height(itemHeight))

                HorizontalDivider(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    thickness = dividerConfig.thickness,
                    color = dividerConfig.color,
                )
            }
        }
    }
}

/**
 * 滚轮选择器项组件
 */
@Composable
private fun <T> WheelPickerItem(
    item: T,
    virtualIndex: Int,
    realIndex: Int,
    state: WheelPickerState,
    itemHeight: Dp,
    visibleItemsCount: Int,
    effect3DConfig: WheelPickerDefaults.Effect3DConfig,
    isItemEnabled: (Int) -> Boolean,
    itemContent: @Composable (item: T, isSelected: Boolean, isEnabled: Boolean) -> Unit,
) {
    val density = LocalDensity.current
    val itemHeightPx = with(density) { itemHeight.toPx() }
    val halfVisibleItems = visibleItemsCount / 2

    // 计算当前项相对于中心的距离
    // 使用remember避免layoutInfo的频繁重组
    val distanceFromCenter by remember(virtualIndex, itemHeightPx, state.lazyListState) {
        derivedStateOf {
            val layoutInfo = state.lazyListState.layoutInfo
            val viewportCenter = layoutInfo.viewportStartOffset + layoutInfo.viewportSize.height / 2f

            val itemInfo = layoutInfo.visibleItemsInfo.find { it.index == virtualIndex }

            if (itemInfo != null) {
                val itemCenter = itemInfo.offset + itemInfo.size / 2f
                (itemCenter - viewportCenter) / itemHeightPx
            } else {
                Float.MAX_VALUE
            }
        }
    }

    // 计算3D效果参数
    val absDistance = abs(distanceFromCenter)
    val isSelected = absDistance < 0.5f
    val isEnabled = isItemEnabled(realIndex)

    val scale =
        if (effect3DConfig.enabled) {
            val normalizedDistance = (absDistance / halfVisibleItems).coerceIn(0f, 1f)
            lerp(effect3DConfig.maxScale, effect3DConfig.minScale, normalizedDistance)
        } else {
            1f
        }

    val alpha =
        if (effect3DConfig.enabled) {
            val normalizedDistance = (absDistance / halfVisibleItems).coerceIn(0f, 1f)
            lerp(effect3DConfig.maxAlpha, effect3DConfig.minAlpha, normalizedDistance)
        } else {
            1f
        }

    val rotationX =
        if (effect3DConfig.enabled) {
            val normalizedDistance = (distanceFromCenter / halfVisibleItems).coerceIn(-1f, 1f)
            lerp(effect3DConfig.maxRotationX, effect3DConfig.minRotationX, abs(normalizedDistance)) * sign(normalizedDistance)
        } else {
            0f
        }

    Box(
        modifier =
            Modifier
                .height(itemHeight)
                .fillMaxWidth()
                .scale(scale)
                .alpha(alpha)
                .graphicsLayer {
                    this.rotationX = rotationX
                },
        contentAlignment = Alignment.Center,
    ) {
        itemContent(item, isSelected, isEnabled)
    }
}

/**
 * 线性插值函数
 */
private fun lerp(
    start: Float,
    stop: Float,
    fraction: Float,
): Float = start + fraction * (stop - start)

/**
 * 自定义Fling行为，实现自动吸附
 */
@Composable
private fun wheelPickerFlingBehavior(
    state: WheelPickerState,
    itemHeight: Dp,
): FlingBehavior {
    val density = LocalDensity.current

    return remember(state, itemHeight, density) {
        object : FlingBehavior {
            override suspend fun ScrollScope.performFling(initialVelocity: Float): Float {
                val decayAnimationSpec = splineBasedDecay<Float>(density)
                val springAnimationSpec =
                    spring<Float>(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessLow,
                    )

                // 执行惯性滚动
                var remainingVelocity = initialVelocity
                if (abs(initialVelocity) > 1f) {
                    var lastValue = 0f
                    AnimationState(
                        initialValue = 0f,
                        initialVelocity = initialVelocity,
                    ).animateDecay(decayAnimationSpec) {
                        val delta = value - lastValue
                        val consumed = scrollBy(delta)
                        lastValue = value
                        remainingVelocity = velocity

                        // 如果滚动被阻止，停止动画
                        if (abs(delta - consumed) > 0.5f) {
                            cancelAnimation()
                        }
                    }
                }

                // 吸附到最近的项
                snapToNearestItem(state)

                return remainingVelocity
            }

            private suspend fun ScrollScope.snapToNearestItem(state: WheelPickerState) {
                // 获取当前布局信息，但避免频繁访问
                val layoutInfo = state.lazyListState.layoutInfo
                val viewportCenter = layoutInfo.viewportStartOffset + layoutInfo.viewportSize.height / 2

                // 找到最接近中心的项
                val centerItem =
                    layoutInfo.visibleItemsInfo.minByOrNull { itemInfo ->
                        val itemCenter = itemInfo.offset + itemInfo.size / 2
                        abs(itemCenter - viewportCenter)
                    }

                if (centerItem != null) {
                    val itemCenter = centerItem.offset + centerItem.size / 2
                    val distanceToCenter = itemCenter - viewportCenter

                    // 平滑滚动到中心
                    if (abs(distanceToCenter) > 1f) {
                        var lastValue = 0f
                        AnimationState(initialValue = 0f)
                            .animateTo(
                                targetValue = distanceToCenter.toFloat(),
                                animationSpec =
                                    spring(
                                        dampingRatio = Spring.DampingRatioNoBouncy,
                                        stiffness = Spring.StiffnessMedium,
                                    ),
                            ) {
                                val delta = value - lastValue
                                scrollBy(delta)
                                lastValue = value
                            }
                    }
                }
            }
        }
    }
}

package com.buque.wakoo.ui.widget.wheelPicker

import android.util.Log
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.buque.wakoo.ui.theme.WakooTheme
import kotlinx.datetime.*

/**
 * WheelPicker使用示例
 */
@Composable
fun WheelPickerExamples() {
    Column(
        modifier =
            Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(32.dp),
    ) {
        // 标题
        Text(
            text = "WheelPicker 示例",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
        )

        // 基础字符串选择器
        BasicStringPickerExample()

        // 数字选择器（无限循环）
        NumberPickerExample()

        // 日期选择器
        DatePickerExample()

        // 时间选择器
        TimePickerExample()

        // 自定义样式选择器
        CustomStylePickerExample()
    }
}

/**
 * 基础字符串选择器示例
 */
@Composable
private fun BasicStringPickerExample() {
    var selectedItem by remember { mutableStateOf("") }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "基础字符串选择器",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            val items =
                remember {
                    listOf("苹果", "香蕉", "橙子", "葡萄", "草莓", "蓝莓", "芒果", "菠萝")
                }

            val state =
                rememberWheelPickerState(
                    initialIndex = 0,
                    itemCount = items.size,
                )

            // 监听选择变化 - 只在滚动停止后更新
            LaunchedEffect(state.snappedIndex) {
                selectedItem = items.getOrNull(state.snappedIndex) ?: ""
            }

            WheelPicker(
                items = items,
                state = state,
                modifier = Modifier.fillMaxWidth(),
                visibleItemsCount = 5,
                itemHeight = 48.dp,
            )

            Text(
                text = "选中项: $selectedItem",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

/**
 * 数字选择器示例（无限循环）
 */
@Composable
private fun NumberPickerExample() {
    var selectedNumber by remember { mutableStateOf(0) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "数字选择器（无限循环）",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            val numbers = remember { (0..99).toList() }

            val state =
                rememberWheelPickerState(
                    initialIndex = 29,
                    itemCount = numbers.size,
                    isInfinite = true,
                )

            // 监听选择变化 - 只在滚动停止后更新
            LaunchedEffect(state.snappedIndex) {
                selectedNumber = numbers.getOrNull(state.snappedIndex) ?: 0
            }

            WheelPicker(
                items = numbers,
                state = state,
                modifier = Modifier.fillMaxWidth(),
                visibleItemsCount = 5,
                itemHeight = 48.dp,
                isInfinite = true,
                itemContent = { number, isSelected ->
                    Text(
                        text = String.format("%02d", number),
                        style = MaterialTheme.typography.bodyLarge,
                        color =
                            if (isSelected) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            },
                        fontSize = if (isSelected) 18.sp else 16.sp,
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                    )
                },
            )

            Text(
                text = "选中数字: $selectedNumber",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

/**
 * 日期选择器示例
 */
@Composable
private fun DatePickerExample() {
    var selectedDate by remember { mutableStateOf(LocalDate.now()) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "日期选择器",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            DateWheelPicker(
                selectedDate = selectedDate,
                onDateChanged = { selectedDate = it },
                modifier = Modifier.fillMaxWidth(),
                yearRange = (2020..2030),
                itemHeight = 48.dp,
                visibleItemsCount = 5,
            )

            Text(
                text = "选中日期: $selectedDate",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

/**
 * 时间选择器示例
 */
@Composable
private fun TimePickerExample() {
    var selectedHour by remember { mutableStateOf(12) }
    var selectedMinute by remember { mutableStateOf(0) }
    var selectedSecond by remember { mutableStateOf(0) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "时间选择器",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            TimeWheelPicker(
                selectedHour = selectedHour,
                selectedMinute = selectedMinute,
                selectedSecond = selectedSecond,
                onTimeChanged = { hour, minute, second ->
                    selectedHour = hour
                    selectedMinute = minute
                    selectedSecond = second
                },
                modifier = Modifier.fillMaxWidth(),
                is24Hour = true,
                showSeconds = true,
                itemHeight = 48.dp,
                visibleItemsCount = 5,
            )

            Text(
                text = "选中时间: ${WheelPickerUtils.formatTime(selectedHour, selectedMinute, selectedSecond)}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

/**
 * 自定义样式选择器示例
 */
@Composable
private fun CustomStylePickerExample() {
    var selectedItem by remember { mutableStateOf("") }

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                text = "自定义样式选择器",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
            )

            val items =
                remember {
                    listOf("小", "中", "大", "特大", "超大")
                }

            val state =
                rememberWheelPickerState(
                    initialIndex = 2,
                    itemCount = items.size,
                )

            // 监听选择变化 - 只在滚动停止后更新
            LaunchedEffect(state.snappedIndex) {
                selectedItem = items.getOrNull(state.snappedIndex) ?: ""
            }

            WheelPicker(
                items = items,
                state = state,
                modifier = Modifier.fillMaxWidth(),
                visibleItemsCount = 3,
                itemHeight = 60.dp,
                dividerConfig =
                    WheelPickerDefaults.DividerConfig(
                        enabled = true,
                        color = Color.Red.copy(alpha = 0.5f),
                        thickness = 2.dp,
                    ),
                effect3DConfig =
                    WheelPickerDefaults.Effect3DConfig(
                        enabled = true,
                        maxScale = 1.2f,
                        minScale = 0.7f,
                        maxAlpha = 1.0f,
                        minAlpha = 0.4f,
                        maxRotationX = 0f,
                        minRotationX = -20f,
                    ),
                itemContent = { item, isSelected ->
                    Text(
                        text = item,
                        style = MaterialTheme.typography.headlineSmall,
                        color =
                            if (isSelected) {
                                Color.Red
                            } else {
                                Color.Gray
                            },
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                    )
                },
            )

            Text(
                text = "选中尺寸: $selectedItem",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
            )

            Log.d("dsfasdfasdfasdfad", "$selectedItem")
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun WheelPickerExamplesPreview() {
    WakooTheme {
        WheelPickerExamples()
    }
}

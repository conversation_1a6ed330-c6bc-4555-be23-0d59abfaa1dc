package com.buque.wakoo.ui.widget.wheelPicker

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import java.time.LocalDate
import java.time.YearMonth
import java.util.*

/**
 * WheelPicker工具类
 * 提供常用的数据生成和转换功能
 */
object WheelPickerUtils {
    
    /**
     * 生成年份列表
     */
    fun generateYears(range: IntRange = (1900..2100)): List<Int> {
        return range.toList()
    }
    
    /**
     * 生成月份列表
     */
    fun generateMonths(): List<Int> {
        return (1..12).toList()
    }
    
    /**
     * 生成指定年月的天数列表
     */
    fun generateDays(year: Int, month: Int): List<Int> {
        val yearMonth = YearMonth.of(year, month)
        return (1..yearMonth.lengthOfMonth()).toList()
    }
    
    /**
     * 生成小时列表
     */
    fun generateHours(is24Hour: Boolean = true): List<Int> {
        return if (is24Hour) (0..23).toList() else (1..12).toList()
    }
    
    /**
     * 生成分钟列表
     */
    fun generateMinutes(): List<Int> {
        return (0..59).toList()
    }
    
    /**
     * 生成秒列表
     */
    fun generateSeconds(): List<Int> {
        return (0..59).toList()
    }
    
    /**
     * 生成数字列表（通用）
     */
    fun generateNumbers(range: IntRange): List<Int> {
        return range.toList()
    }
    
    /**
     * 生成字符串列表（通用）
     */
    fun generateStrings(items: Array<String>): List<String> {
        return items.toList()
    }
    
    /**
     * 将LocalDate转换为年月日索引
     */
    fun dateToIndices(
        date: LocalDate,
        yearRange: IntRange = (1900..2100)
    ): Triple<Int, Int, Int> {
        val years = generateYears(yearRange)
        val yearIndex = years.indexOf(date.year).coerceAtLeast(0)
        val monthIndex = date.monthValue - 1
        val dayIndex = date.dayOfMonth - 1
        return Triple(yearIndex, monthIndex, dayIndex)
    }
    
    /**
     * 将年月日索引转换为LocalDate
     */
    fun indicesToDate(
        yearIndex: Int,
        monthIndex: Int,
        dayIndex: Int,
        yearRange: IntRange = (1900..2100)
    ): LocalDate {
        val years = generateYears(yearRange)
        val months = generateMonths()
        
        val year = years.getOrNull(yearIndex) ?: LocalDate.now().year
        val month = months.getOrNull(monthIndex) ?: 1
        val days = generateDays(year, month)
        val day = days.getOrNull(dayIndex) ?: 1
        
        return LocalDate.of(year, month, day)
    }
    
    /**
     * 创建安全的日期，避免无效日期
     */
    fun createSafeDate(year: Int, month: Int, day: Int): LocalDate {
        val yearMonth = YearMonth.of(year, month)
        val safeDay = day.coerceIn(1, yearMonth.lengthOfMonth())
        return LocalDate.of(year, month, safeDay)
    }
    
    /**
     * 格式化数字为两位数字符串
     */
    fun formatTwoDigits(number: Int): String {
        return String.format("%02d", number)
    }
    
    /**
     * 格式化时间
     */
    fun formatTime(hour: Int, minute: Int, second: Int? = null): String {
        return if (second != null) {
            "${formatTwoDigits(hour)}:${formatTwoDigits(minute)}:${formatTwoDigits(second)}"
        } else {
            "${formatTwoDigits(hour)}:${formatTwoDigits(minute)}"
        }
    }
    
    /**
     * 格式化日期
     */
    fun formatDate(year: Int, month: Int, day: Int, separator: String = "-"): String {
        return "${year}${separator}${formatTwoDigits(month)}${separator}${formatTwoDigits(day)}"
    }
    
    /**
     * 中文月份名称
     */
    val ChineseMonths = arrayOf(
        "一月", "二月", "三月", "四月", "五月", "六月",
        "七月", "八月", "九月", "十月", "十一月", "十二月"
    )
    
    /**
     * 英文月份名称
     */
    val EnglishMonths = arrayOf(
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    )
    
    /**
     * 英文月份简称
     */
    val EnglishMonthsShort = arrayOf(
        "Jan", "Feb", "Mar", "Apr", "May", "Jun",
        "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    )
    
    /**
     * 中文星期名称
     */
    val ChineseWeekdays = arrayOf(
        "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"
    )
    
    /**
     * 英文星期名称
     */
    val EnglishWeekdays = arrayOf(
        "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"
    )
    
    /**
     * 英文星期简称
     */
    val EnglishWeekdaysShort = arrayOf(
        "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"
    )
}

/**
 * Composable扩展函数，用于记住生成的数据
 */
@Composable
fun rememberYears(range: IntRange = (1900..2100)): List<Int> {
    return remember(range) { WheelPickerUtils.generateYears(range) }
}

@Composable
fun rememberMonths(): List<Int> {
    return remember { WheelPickerUtils.generateMonths() }
}

@Composable
fun rememberDays(year: Int, month: Int): List<Int> {
    return remember(year, month) { WheelPickerUtils.generateDays(year, month) }
}

@Composable
fun rememberHours(is24Hour: Boolean = true): List<Int> {
    return remember(is24Hour) { WheelPickerUtils.generateHours(is24Hour) }
}

@Composable
fun rememberMinutes(): List<Int> {
    return remember { WheelPickerUtils.generateMinutes() }
}

@Composable
fun rememberSeconds(): List<Int> {
    return remember { WheelPickerUtils.generateSeconds() }
}

@Composable
fun rememberNumbers(range: IntRange): List<Int> {
    return remember(range) { WheelPickerUtils.generateNumbers(range) }
}

@Composable
fun rememberStrings(items: Array<String>): List<String> {
    return remember(items) { WheelPickerUtils.generateStrings(items) }
}
